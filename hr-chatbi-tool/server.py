#!/usr/bin/env python3
"""
hr-chatbi-tool MCP 工具
智能数据获取和分析工具
"""

import json
import os
from typing import Dict, List, Any, Optional, Coroutine
from dataclasses import dataclass

import httpx
from loguru import logger

from dotenv import load_dotenv

from mcp.server.fastmcp import FastMCP

# 加载环境变量
load_dotenv()

# 初始化 FastMCP server，配置为无状态HTTP模式以支持多协议
mcp = FastMCP("hr-chatbi-tool", stateless_http=True)

# 配置日志
logger.add("logs/hr-chatbi-tool.log", rotation="1 day", retention="7 days")


# 数据模型
@dataclass
class APIConfig:
    """API配置"""
    name: str
    base_url: str
    headers: Dict[str, str]
    timeout: int = 30


# API配置
API_CONFIGS = {
    "chatbi": APIConfig(
        name="ChatBI智能数据API",
        base_url="https://aicopilot-apis.coli688.com",
        headers={"Content-Type": "application/json"},
        timeout=300
    )
}


async def fetch_query_chat(user_id: str, agent_id: int, chat_id: int, query_text: str) -> dict[str, Any]:
    """获取ChatBI对话查询数据

    向ChatBI API发送查询请求并获取分析结果

    Args:
        user_id: 用户ID，标识用户身份
        agent_id: 场景ID，标识应用场景
        chat_id: 对话ID，标识对话会话
        query_text: 查询文本，描述需要获取的数据

    Returns:
        包含查询结果的字典，成功时返回完整响应，失败时返回错误信息

    Raises:
        Exception: API返回非200状态码或请求过程中发生错误
    """
    try:
        config = API_CONFIGS["chatbi"]
        request_params = {
            "agentId": str(agent_id),
            "chatId": str(chat_id),
            "queryText": str(query_text)
        }
        # 准备认证和请求头
        auth_credentials = httpx.BasicAuth(
            username=os.getenv("CHATBI_API_USERNAME", "7c2LbIcuqg4ZB7aIXSSdAwtr3JNlIw5c"),
            password=os.getenv("CHATBI_API_PASSWORD", "Rp3ORy2NaXCTvkczgwDIF3ywVudygqYO")
        )
        headers = config.headers.copy()
        headers.update({
            'User-Code': user_id,
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        })

        # 发送请求
        async with httpx.AsyncClient(timeout=config.timeout) as client:
            url = f"{config.base_url}/chatbi/oip/chat/query"
            response = await client.post(
                url,
                json=request_params,
                headers=headers,
                auth=auth_credentials
            )

            # 处理响应
            if response.status_code == 200:
                data = response.json()
                if data.get("code") != 200:
                    raise Exception(f"{data.get('msg')}")
                return data
            else:
                logger.warning(f"智能数据API请求失败: HTTP {response.status_code}")
                return {"error": f"智能数据API请求失败: HTTP {response.status_code}"}

    except Exception as e:
        logger.error(f"获取智能数据失败: {str(e)}")
        return {"error": f"获取智能数据失败: {str(e)}"}


def filter_sensitive_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """过滤敏感数据，控制对外输出内容

    完全移除指定的敏感字段，而不是替换它们的值

    Args:
        data: 原始API返回的数据

    Returns:
        过滤后的数据，敏感字段被完全移除
    """
    # 如果数据为空，直接返回
    if not data:
        return data

    # 创建数据副本，避免修改原始数据
    filtered = data.copy()

    # 需要过滤的敏感字段列表
    sensitive_fields = [
        "sql"
    ]

    # 如果数据是字典类型
    if isinstance(data, dict):
        # 创建新字典，只包含非敏感字段
        filtered = {}

        for key, value in data.items():
            # 跳过敏感字段
            if key.lower() in [field.lower() for field in sensitive_fields]:
                continue

            # 递归处理嵌套结构
            if isinstance(value, (dict, list)):
                filtered[key] = filter_sensitive_data(value)
            else:
                filtered[key] = value

    # 如果数据是列表类型
    elif isinstance(data, list):
        # 递归处理列表中的每个元素
        filtered = [filter_sensitive_data(item) if isinstance(item, (dict, list)) else item
                    for item in data]
    else:
        # 非容器类型直接返回
        return data

    return filtered


@mcp.tool()
async def query_hr_data(user_id: str, query_text: str, chat_id: int) -> str:
    """Hr ChatBI，获取人力资源相关数据，当用户咨询人力相关问题时，可以使用该工具进行数据获取。如：近5年688整体硕博比例的变化趋势与分布

    Args:
        user_id: 用户ID，用于标识不同的用户
        query_text: 查询文本，描述需要获取的数据类型
        chat_id: 对话ID，用于标识不同的对话会话，不传时或者传0默认新建会话

    Returns:
    {
      "chatId": 3775,
      "queryId": 25452,
      "queryText": "职级为05的员工在运营线有多少",
      "parseTimeCost": 6814,
      "text2SqlTimeCost": 5647,
      "queryColumns": [
        {
          "name": "员工数量",
          "type": "BIGINT",
          "nameEn": "员工数量",
          "showType": "NUMBER",
          "authorized": true,
          "dataFormatType": null,
          "dataFormat": null,
          "comment": null
        }
      ],
      "queryResults": [
        {
          "员工数量": 17
        }
      ],
      "queryTimeCost": 1552
    }
    """
    agent_id = 47
    logger.info(f"用户信息 {user_id}, 场景ID {agent_id}, 对话ID {chat_id}, 对话文本 {query_text}")
    try:
        result = await fetch_query_chat(user_id, agent_id, chat_id, query_text)
        raw_data = result.get("data", {})
        if raw_data:
            filtered_data = filter_sensitive_data(raw_data)
            # 针对数据格式化处理，按需对外输出
            return json.dumps(filtered_data, ensure_ascii=False, indent=2)
        else:
            return result
    except Exception as e:
        return json.dumps({"error": str(e)}, ensure_ascii=False, indent=2)


def main():
    """主函数，启动MCP服务器，使用Streamable HTTP协议"""
    mcp.run(transport='streamable-http')


if __name__ == "__main__":
    main()
