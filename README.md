# Coli AI MCP Server

这是一个多MCP工具统一管理服务器项目，支持多个MCP工具的集中部署和管理。

## 🚀 项目简介

本项目是一个现代化的MCP服务器，支持多种传输协议（Streamable HTTP、SSE、stdio），专门用于收集、存储和管理各种MCP工具。通过统一的FastAPI服务器，可以同时运行多个MCP工具，为AI模型提供丰富的功能扩展。

## ✨ 主要特性

- **🔍 自动发现**: 自动扫描和注册新的MCP工具，无需手动配置
- **🚀 零配置添加**: 使用`manage_tools.py`一键创建新工具模板
- **📡 多协议支持**: 支持Streamable HTTP、SSE、stdio等多种传输协议
- **🔧 统一管理**: 通过单一服务器和注册器管理所有MCP工具
- **⚙️ 配置驱动**: 每个工具独立的config.json配置文件
- **🔄 动态生成**: 自动生成API响应和客户端配置示例

## 🛠️ 内置工具

### 1. Webpage Generator (网页生成器) 
- **功能**: 使用大模型动态生成完整的HTML网页
- **端点**: `/webpage-generator/mcp` (HTTP) | `/webpage-generator-sse/sse` (SSE)
- **特性**:
  - 支持任意内容的网页生成和修改
  - **🌐 动态域名访问**: 每个生成的网页自动分配唯一子域名 (xxx.domain.com)
  - **🎯 自定义子域名**: 支持指定自定义子域名
  - **📋 域名管理**: 提供域名映射查询和管理功能

### 2. Weather Tool (天气工具)
- **功能**: 获取美国境内任意位置的天气预报和当前天气
- **端点**: `/weather/mcp` (HTTP) | `/weather-sse/sse` (SSE)
- **特性**: 基于NWS API，提供详细的天气信息

### 3. Prompt Manager (提示词管理器)
- **功能**: 管理和获取各种AI提示词模板
- **端点**: `/prompt-manager/mcp` (HTTP) | `/prompt-manager-sse/sse` (SSE)
- **特性**: 提示词存储、搜索、创建和管理

## 🏗️ 项目结构

```
coli-ai-mcp-server/
├── main.py                     # 主服务器，自动发现和管理所有MCP工具
├── mcp_registry.py            # MCP工具注册器，自动化管理核心
├── manage_tools.py            # 工具管理命令行工具
├── test_tools.py              # 测试套件
├── deploy.py                  # 部署脚本
├── requirements.txt           # 项目依赖
├── README.md
├── webpage-generator/         # 网页生成工具
│   ├── server.py             # MCP服务器实现
│   ├── config.json           # 工具配置文件
│   ├── requirements.txt      # 工具依赖
│   ├── prompt.txt            # 生成提示词
│   └── modify_prompt.txt     # 修改提示词
├── weather-tool/             # 天气查询工具
│   ├── server.py             # MCP服务器实现
│   ├── config.json           # 工具配置文件
│   └── requirements.txt      # 工具依赖
├── prompt-manager/           # 提示词管理工具
│   ├── server.py             # MCP服务器实现
│   ├── config.json           # 工具配置文件
│   ├── requirements.txt      # 工具依赖
│   └── prompts/              # 提示词文件目录
│       ├── webpage_generation.txt
│       ├── code_review.txt
│       └── api_documentation.txt
└── your-new-tool/            # 新工具（自动发现）
    ├── server.py             # 自动生成的模板
    ├── config.json           # 自动生成的配置
    └── requirements.txt      # 自动生成的依赖
```

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装主项目依赖
pip install -r requirements.txt

# 或使用uv（推荐）
uv sync
```

### 2. 启动服务器

```bash
# 启动统一管理服务器
python main.py
```

服务器启动后，可以通过以下端点访问各个MCP工具：
- 主服务器: http://localhost:8000
- 网页生成器: http://localhost:8000/webpage-generator/mcp
- 天气工具: http://localhost:8000/weather/mcp
- 提示词管理器: http://localhost:8000/prompt-manager/mcp
- 健康检查: http://localhost:8000/health

### 3. 环境变量配置

对于网页生成器，需要配置OpenAI API：

```bash
export OPENAI_API_KEY="your-api-key"
export OPENAI_BASE_URL="https://api.openai.com/v1"  # 可选
export OPENAI_MODEL="gpt-4o"  # 可选
```

### 4. 验证服务器

访问服务器端点验证功能：

```bash
# 检查服务器状态
curl http://localhost:8000/health

# 查看所有可用工具
curl http://localhost:8000

# 测试具体工具端点
curl http://localhost:8000/webpage-generator/mcp
curl http://localhost:8000/weather/mcp
curl http://localhost:8000/prompt-manager/mcp
```

## 🔧 添加新的 MCP 工具

### 🚀 快速创建（推荐）

使用工具管理命令一键创建新的MCP工具：

```bash
# 创建新工具（自动生成完整模板）
python manage_tools.py create your-tool-name

# 验证工具配置
python manage_tools.py validate your-tool-name

# 重启服务器（自动发现并注册新工具）
python main.py
```

### 📋 工具管理命令

```bash
# 列出所有已发现的工具
python manage_tools.py list

# 创建新工具模板
python manage_tools.py create <tool-name>

# 验证工具配置和代码
python manage_tools.py validate <tool-name>
```

### 🛠️ 手动创建

如果需要手动创建，请按以下步骤：

#### 1. 创建工具目录和文件

```bash
mkdir your-tool-name
cd your-tool-name
```

#### 2. 创建核心文件

**server.py** - MCP服务器实现：
```python
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("your-tool-name", stateless_http=True)

@mcp.tool()
async def your_function(param: str) -> str:
    """工具函数描述"""
    return f"处理结果: {param}"

if __name__ == "__main__":
    mcp.run(transport='streamable-http')
```

**config.json** - 工具配置：
```json
{
  "display_name": "Your Tool Name",
  "description": "工具描述",
  "version": "1.0.0",
  "author": "Your Name",
  "requires_env": [],
  "endpoints": {
    "http": "/your-tool-name/mcp",
    "sse": "/your-tool-name-sse/sse"
  }
}
```

**requirements.txt** - 依赖管理：
```txt
mcp[cli]>=1.9.0
# 其他依赖...
```

#### 3. 重启服务器
新工具将被自动发现和注册，无需修改`main.py`。

### ✨ 自动化特性

- **零配置注册**：新工具自动被发现和注册
- **动态端点生成**：HTTP和SSE端点自动创建
- **配置驱动**：通过config.json管理工具元数据
- **模板生成**：自动创建完整的工具模板

## 🔌 MCP客户端连接

### 📡 支持的协议

- **Streamable HTTP**（推荐）: `http://localhost:8000/{tool-name}/mcp`
- **SSE**: `http://localhost:8000/{tool-name}-sse/sse`
- **stdio**: 直接运行Python脚本

### 🔧 获取配置

访问主服务器获取完整的客户端配置示例：

```bash
curl http://localhost:8000
```

返回包含所有工具的端点信息和客户端配置示例。

### 🌍 环境变量

```bash
# 网页生成器需要OpenAI API
export OPENAI_API_KEY="your-api-key"
export OPENAI_BASE_URL="https://api.openai.com/v1"  # 可选
export OPENAI_MODEL="gpt-4o"  # 可选
```

## 🔍 故障排除

### 🚨 常见问题

| 问题 | 解决方案 |
|------|----------|
| 服务器启动失败 | `lsof -i :8000` 检查端口占用<br>`pip install -r requirements.txt` 安装依赖 |
| 工具调用失败 | 检查网络连接和防火墙<br>尝试不同传输协议 |
| 网页生成器不工作 | 设置 `OPENAI_API_KEY` 环境变量<br>检查API密钥权限 |
| 天气工具错误 | 确认查询美国境内坐标<br>使用十进制度数格式 |

### 🛠️ 调试命令

```bash
# 快速检查
curl http://localhost:8000/health

# 查看所有工具和配置
curl http://localhost:8000

# 运行完整测试
python test_tools.py

# 管理工具
python manage_tools.py list
```

## 🚀 部署和开发

### 📦 快速部署

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量（可选）
export OPENAI_API_KEY="your-api-key"

# 启动服务器
python main.py
```

### 🧪 测试

```bash
# 运行测试套件
python test_tools.py

# 使用部署脚本（包含测试和配置检查）
python deploy.py
```

### 🐳 Docker部署

```dockerfile
FROM python:3.12-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "main.py"]
```

## 📚 相关链接

- [MCP协议规范](https://modelcontextprotocol.io/)
- [FastMCP文档](https://github.com/jlowin/fastmcp)
- [项目仓库](http://gitlab.coli688.com/ai/coli-ai-mcp-server)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

---

**Coli AI MCP Server** - 现代化的多MCP工具统一管理服务器 🚀

