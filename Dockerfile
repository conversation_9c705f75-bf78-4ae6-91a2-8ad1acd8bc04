# 使用指定的基础镜像
FROM ***********:30005/688-kj-ai/python:3.10

# 设置工作目录
WORKDIR /app

# 配置pip使用清华镜像源
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 复制项目文件
COPY . .

# 安装主项目依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 遍历所有子目录，安装各个工具的依赖
RUN find . -name "requirements.txt" -not -path "./requirements.txt" -exec pip install --no-cache-dir -r {} \;

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "main.py"]
