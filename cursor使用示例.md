# Cursor IDE MCP 配置指南

## 🎯 前提条件

确保您的MCP服务器正在运行：
```bash
python main.py
```

服务器应该在 `http://localhost:8000` 运行。

## 🔧 配置方式

### 方式一：Streamable HTTP（推荐）

创建或编辑Cursor的MCP配置文件：

**配置文件位置：**
- **项目级别**：`.cursor/mcp.json`（仅在当前项目中可用）
- **全局级别**：`~/.cursor/mcp.json`（在所有项目中可用）
- **Cursor官方位置**：
  - macOS: `~/Library/Application Support/Cursor/User/globalStorage/rooveterinaryinc.cursor-mcp/settings.json`
  - Windows: `%APPDATA%\Cursor\User\globalStorage\rooveterinaryinc.cursor-mcp\settings.json`
  - Linux: `~/.config/Cursor/User/globalStorage/rooveterinaryinc.cursor-mcp\settings.json`

**配置内容：**
```json
{
  "mcpServers": {
    "webpage-generator": {
      "url": "http://localhost:8000/webpage-generator/mcp/",
      "transport": "streamable-http"
    },
    "weather-tool": {
      "url": "http://localhost:8000/weather/mcp/",
      "transport": "streamable-http"
    },
    "prompt-manager": {
      "url": "http://localhost:8000/prompt-manager/mcp/",
      "transport": "streamable-http"
    }
  }
}
```

### 方式二：SSE

**配置内容：**
```json
{
  "mcpServers": {
    "webpage-generator": {
      "url": "http://localhost:8000/webpage-generator-sse/sse",
      "transport": "sse"
    },
    "weather-tool": {
      "url": "http://localhost:8000/weather-sse/sse",
      "transport": "sse"
    },
    "prompt-manager": {
      "url": "http://localhost:8000/prompt-manager-sse/sse",
      "transport": "sse"
    }
  }
}
```

## 🧪 测试MCP工具

重启Cursor后，测试以下命令：

### 测试网页生成器
```
"帮我生成一个产品展示页面，包含产品名称、描述、特性列表和联系方式"
```

### 测试天气工具
```
"查询纽约市的天气预报"
"获取洛杉矶的当前天气条件"
```

### 测试提示词管理器
```
"给我一个代码审查的提示词"
"列出所有可用的提示词模板"
```

## 🔍 故障排除

### 问题1：MCP工具没有出现
1. 确认服务器正在运行：`curl http://localhost:8000/health`
2. 检查配置文件路径和格式
3. 重启Cursor
4. 查看Cursor开发者工具中的错误信息

### 问题2：工具调用失败
1. 检查网络连接
2. 确认端口8000没有被防火墙阻止
3. 尝试不同的传输方式

### 问题3：网页生成器不工作
1. 确认设置了OPENAI_API_KEY环境变量
2. 检查API密钥是否有效
3. 查看服务器日志中的错误信息
