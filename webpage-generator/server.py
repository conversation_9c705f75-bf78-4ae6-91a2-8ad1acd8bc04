#!/usr/bin/env python3
"""
通用网页生成 MCP 工具
使用大模型动态生成单一、完整、可直接运行的 HTML 文件
支持SSE协议和动态域名访问
"""

import os
import re
import json
import uuid
import string
import random
from typing import Dict, Optional
from mcp.server.fastmcp import FastMCP
from openai import AsyncOpenAI


# 初始化 FastMCP server，配置为无状态HTTP模式以支持SSE
mcp = FastMCP("webpage-generator", stateless_http=True)

# 配置
DEFAULT_MODEL = "ep-20250228152640-zqrj8"
DEFAULT_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"
DEFAULT_API_KEY = "5da26e76-6eed-40ff-bdef-5c886759dc0c"
# todo
DEFAULT_DOMAIN = "xiaohai-test.coli688.com"  # 默认域名，可通过环境变量覆盖
STATIC_FILES_DIR = os.path.join(os.path.dirname(__file__), "static_files")  # 静态文件存储目录


class DomainMappingManager:
    """域名映射管理器"""

    def __init__(self, mapping_file: str = os.path.join(os.path.dirname(__file__), "domain_mappings.json")):
        self.mapping_file = mapping_file
        self.mappings = self._load_mappings()

    def _load_mappings(self) -> Dict[str, str]:
        """加载域名映射"""
        if os.path.exists(self.mapping_file):
            try:
                with open(self.mapping_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}

    def _save_mappings(self):
        """保存域名映射"""
        os.makedirs(os.path.dirname(self.mapping_file), exist_ok=True)
        with open(self.mapping_file, 'w', encoding='utf-8') as f:
            json.dump(self.mappings, f, ensure_ascii=False, indent=2)

    def generate_subdomain(self, length: int = 8) -> str:
        """生成随机子域名"""
        chars = string.ascii_lowercase + string.digits
        while True:
            subdomain = ''.join(random.choice(chars) for _ in range(length))
            if subdomain not in self.mappings:
                return subdomain

    def register_domain(self, html_content: str, custom_subdomain: Optional[str] = None) -> Dict[str, str]:
        """注册域名映射"""
        # 生成子域名
        subdomain = custom_subdomain or self.generate_subdomain()

        # 确保静态文件目录存在
        os.makedirs(STATIC_FILES_DIR, exist_ok=True)

        # 生成文件路径
        filename = f"{subdomain}.html"
        file_path = os.path.join(STATIC_FILES_DIR, filename)

        # 保存HTML文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        # 注册映射
        domain = os.getenv("DOMAIN", DEFAULT_DOMAIN)
        full_domain = f"{subdomain}.{domain}"
        self.mappings[subdomain] = {
            "file_path": file_path,
            "filename": filename,
            "created_at": str(uuid.uuid4()),  # 使用UUID作为创建时间戳
            "full_domain": full_domain
        }

        # 保存映射
        self._save_mappings()

        return {
            "subdomain": subdomain,
            "full_domain": full_domain,
            "file_path": file_path,
            "access_url": f"http://{full_domain}"
        }

    def get_mapping(self, subdomain: str) -> Optional[Dict[str, str]]:
        """获取域名映射"""
        return self.mappings.get(subdomain)

    def list_mappings(self) -> Dict[str, Dict[str, str]]:
        """列出所有映射"""
        return self.mappings.copy()


# 全局域名映射管理器
domain_manager = DomainMappingManager()


def load_prompt() -> str:
    """加载生成网页的提示词文件"""
    prompt_path = os.path.join(os.path.dirname(__file__), "prompt.txt")
    with open(prompt_path, 'r', encoding='utf-8') as f:
        return f.read().strip()


def load_modify_prompt() -> str:
    """加载修改网页的提示词文件"""
    prompt_path = os.path.join(os.path.dirname(__file__), "modify_prompt.txt")
    with open(prompt_path, 'r', encoding='utf-8') as f:
        return f.read().strip()


def get_api_config() -> Dict[str, str]:
    """获取API配置"""
    return {
        "api_key": os.getenv("OPENAI_API_KEY", DEFAULT_API_KEY),
        "base_url": os.getenv("OPENAI_BASE_URL", DEFAULT_BASE_URL),
        "model": os.getenv("OPENAI_MODEL", DEFAULT_MODEL)
    }


def clean_html_content(content: str) -> str:
    """清理HTML内容中的markdown代码块标记"""
    if not content:
        return content

    # 移除开头的markdown代码块标记
    # 支持 ```html, ```HTML, ``` 等各种形式
    content = re.sub(r'^```(?:html|HTML)?\s*\n?', '', content, flags=re.MULTILINE)

    # 移除结尾的markdown代码块标记
    content = re.sub(r'\n?```\s*$', '', content, flags=re.MULTILINE)

    # 移除可能存在的多余空行
    content = re.sub(r'^\s*\n', '', content)
    content = re.sub(r'\n\s*$', '', content)

    return content


async def call_llm(prompt: str, content: str) -> str:
    """调用大模型生成HTML，优化max_tokens处理"""
    config = get_api_config()

    if not config["api_key"]:
        raise ValueError("请设置 OPENAI_API_KEY 环境变量")

    client = None
    try:
        # 初始化OpenAI客户端
        client = AsyncOpenAI(
            api_key=config["api_key"],
            base_url=config["base_url"]
        )

        response = await client.chat.completions.create(
            model=config["model"],
            messages=[
                {
                    "role": "system",
                    "content": prompt
                },
                {
                    "role": "user",
                    "content": content
                }
            ],
            temperature=0.7
        )

        if not response.choices or not response.choices[0].message.content:
            raise Exception("模型返回了空的响应")

        html_content = response.choices[0].message.content

        # 清理可能的markdown标记
        html_content = clean_html_content(html_content)

        return html_content.strip()

    except Exception as e:
        raise Exception(f"生成HTML失败: {str(e)}")
    finally:
        if client:
            await client.close()


@mcp.tool()
async def generate_webpage(content: str, custom_subdomain: Optional[str] = None) -> str:
    """
    使用大模型生成基于源文档的单一、完整、可直接运行的 HTML 文件

    Args:
        content: 源文档内容（可以是任何类型的文档：财务报告、产品介绍、技术文档、新闻文章等）
        custom_subdomain: 自定义子域名（可选，如果不提供则自动生成）

    Returns:
        完整的访问URL
    """
    try:
        # 加载提示词
        prompt = load_prompt()

        # 调用大模型生成HTML
        html_content = await call_llm(prompt, content)

        # 注册域名映射并保存到static_files
        domain_info = domain_manager.register_domain(html_content, custom_subdomain)

        # 返回完整的访问URL
        return domain_info["access_url"]

    except Exception as e:
        raise Exception(f"生成失败: {str(e)}")


@mcp.tool()
async def modify_webpage(html_content: str, modification_requirements: str,
                         custom_subdomain: Optional[str] = None) -> str:
    """
    修改已有的HTML网页内容

    Args:
        html_content: 已有的HTML内容
        modification_requirements: 修改要求描述
        custom_subdomain: 自定义子域名（可选，如果不提供则自动生成）

    Returns:
        修改后的完整访问URL
    """
    try:
        # 加载修改提示词
        base_prompt = load_modify_prompt()

        # 构建完整的修改提示词
        modification_prompt = f"""{base_prompt}

修改要求：
{modification_requirements}

现有的HTML内容：
{html_content}

请输出修改后的完整HTML代码："""

        # 调用大模型进行修改
        modified_html = await call_llm(modification_prompt, "")

        # 注册域名映射并保存到static_files
        domain_info = domain_manager.register_domain(modified_html, custom_subdomain)

        # 返回完整的访问URL
        return domain_info["access_url"]

    except Exception as e:
        raise Exception(f"修改失败: {str(e)}")


@mcp.tool()
async def list_domain_mappings() -> str:
    """
    列出所有已注册的域名映射

    Returns:
        包含所有域名映射信息的JSON字符串
    """
    try:
        mappings = domain_manager.list_mappings()
        return json.dumps(mappings, ensure_ascii=False, indent=2)
    except Exception as e:
        raise Exception(f"获取域名映射失败: {str(e)}")


@mcp.tool()
async def get_domain_info(subdomain: str) -> str:
    """
    获取指定子域名的映射信息

    Args:
        subdomain: 子域名

    Returns:
        域名映射信息的JSON字符串
    """
    try:
        mapping = domain_manager.get_mapping(subdomain)
        if mapping:
            return json.dumps(mapping, ensure_ascii=False, indent=2)
        else:
            return json.dumps({"error": f"未找到子域名 '{subdomain}' 的映射"}, ensure_ascii=False)
    except Exception as e:
        raise Exception(f"获取域名信息失败: {str(e)}")


@mcp.tool()
async def register_existing_html(file_path: str, custom_subdomain: Optional[str] = None) -> str:
    """
    为已存在的HTML文件注册域名访问

    Args:
        file_path: HTML文件的路径
        custom_subdomain: 自定义子域名（可选）

    Returns:
        域名注册信息的JSON字符串
    """
    try:
        if not os.path.exists(file_path):
            raise Exception(f"文件不存在: {file_path}")

        # 读取HTML内容
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # 注册域名映射
        domain_info = domain_manager.register_domain(html_content, custom_subdomain)

        return json.dumps(domain_info, ensure_ascii=False, indent=2)

    except Exception as e:
        raise Exception(f"注册域名失败: {str(e)}")





def main():
    """主函数，启动MCP服务器，使用Streamable HTTP协议"""
    mcp.run(transport='streamable-http')


if __name__ == "__main__":
    main()
