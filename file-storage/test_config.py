#!/usr/bin/env python3
"""
测试OSS配置功能
"""

import asyncio
from server import storage_manager, OSSConfig

async def test_config_functionality():
    """测试配置功能"""
    print("🔧 测试OSS配置功能")
    print("=" * 50)
    
    # 1. 测试配置管理器
    print("\n1. 测试配置管理器:")
    config_manager = OSSConfig()
    print(f"默认配置信息:\n{config_manager.get_config_info()}")
    
    # 2. 测试配置获取
    print("\n2. 测试配置获取:")
    config = config_manager.get_config()
    print(f"获取到的配置: {list(config.keys())}")
    
    # 3. 测试配置验证
    print("\n3. 测试配置验证:")
    is_valid, message = config_manager.validate_config(config)
    print(f"配置验证结果: {is_valid}, 消息: {message}")
    
    # 4. 测试参数覆盖
    print("\n4. 测试参数覆盖:")
    custom_config = config_manager.get_config(
        access_key_id="test-key-id",
        bucket_name="test-bucket"
    )
    print(f"自定义配置: access_key_id={custom_config['access_key_id'][:8]}..., bucket_name={custom_config['bucket_name']}")
    
    # 5. 测试存储管理器
    print("\n5. 测试存储管理器:")
    print(f"OSS可用性: {storage_manager.is_available()}")
    if storage_manager.config_manager:
        print("配置管理器已初始化 ✅")
    else:
        print("配置管理器未初始化 ❌")
    
    print("\n✅ 配置功能测试完成!")

if __name__ == "__main__":
    asyncio.run(test_config_functionality())
