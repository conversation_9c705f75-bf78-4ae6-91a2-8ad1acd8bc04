# 文件存储工具

一个强大的MCP工具，支持将文件上传到多种云存储服务，包括阿里云OSS、AWS S3和Azure Blob Storage。

## 🚀 功能特性

- **多云存储支持**: 支持阿里云OSS、AWS S3、Azure Blob Storage
- **文件上传**: 支持Base64内容上传和本地文件上传
- **文件管理**: 文件列表查看、删除、信息获取
- **访问URL生成**: 生成带有效期的文件访问链接
- **自动文件类型检测**: 支持MIME类型自动识别
- **时间戳命名**: 自动添加时间戳前缀避免文件重名
- **配置灵活**: 支持环境变量和参数传递两种配置方式

## 🛠️ 工具列表

### upload_file_to_cloud
将Base64编码的文件内容上传到云存储
- **参数**:
  - `file_content_base64` (必需): Base64编码的文件内容
  - `filename` (必需): 文件名
  - `provider` (可选): 存储提供商，默认"oss"
  - `**storage_config`: 存储配置参数
- **返回**: 上传结果信息和访问链接

### upload_local_file_to_cloud
将本地文件上传到云存储
- **参数**:
  - `file_path` (必需): 本地文件路径
  - `provider` (可选): 存储提供商，默认"oss"
  - `object_name` (可选): 云存储中的对象名
  - `**storage_config`: 存储配置参数
- **返回**: 上传结果信息和访问链接

### list_cloud_files
列出云存储中的文件
- **参数**:
  - `provider` (可选): 存储提供商，默认"oss"
  - `prefix` (可选): 文件名前缀过滤
  - `**storage_config`: 存储配置参数
- **返回**: 文件列表信息

### delete_cloud_file
删除云存储中的文件
- **参数**:
  - `object_name` (必需): 要删除的对象名
  - `provider` (可选): 存储提供商，默认"oss"
  - `**storage_config`: 存储配置参数
- **返回**: 删除结果信息

### get_cloud_file_url
获取云存储文件的访问URL
- **参数**:
  - `object_name` (必需): 对象名
  - `provider` (可选): 存储提供商，默认"oss"
  - `expires_in` (可选): URL过期时间（秒），默认3600
  - `**storage_config`: 存储配置参数
- **返回**: 文件访问URL和过期时间

### get_storage_info
获取文件存储工具的信息和使用说明
- **返回**: 工具功能介绍和配置指南

## 📚 资源列表

### file-storage://providers
获取所有可用的存储提供商信息
- **返回**: 提供商列表、可用性状态和配置要求

### file-storage://config/{provider}
获取指定存储提供商的详细配置信息
- **参数**: `provider` - 存储提供商名称 (oss/s3/azure)
- **返回**: 配置项说明和环境变量示例

## 💡 使用示例

### 基本文件上传

```python
# 上传Base64编码的文件
result = await upload_file_to_cloud(
    file_content_base64="iVBORw0KGgoAAAANSUhEUgAA...",
    filename="image.png",
    provider="oss"
)

# 上传本地文件
result = await upload_local_file_to_cloud(
    file_path="/path/to/local/file.pdf",
    provider="s3",
    object_name="documents/report.pdf"
)
```

### 文件管理

```python
# 列出文件
files = await list_cloud_files(
    provider="oss",
    prefix="images/"
)

# 获取文件访问URL
url = await get_cloud_file_url(
    object_name="20241201_120000_image.png",
    provider="oss",
    expires_in=7200  # 2小时有效期
)

# 删除文件
result = await delete_cloud_file(
    object_name="20241201_120000_old_file.pdf",
    provider="azure"
)
```

### 带配置参数的上传

```python
# OSS上传（传递配置参数）
result = await upload_file_to_cloud(
    file_content_base64="...",
    filename="test.jpg",
    provider="oss",
    access_key_id="your-key-id",
    access_key_secret="your-key-secret",
    endpoint="https://oss-cn-beijing.aliyuncs.com",
    bucket_name="your-bucket"
)

# S3上传（传递配置参数）
result = await upload_file_to_cloud(
    file_content_base64="...",
    filename="test.jpg",
    provider="s3",
    access_key="your-access-key",
    secret_key="your-secret-key",
    bucket_name="your-bucket",
    region="us-west-2"
)
```

## 🔧 配置说明

### 阿里云OSS配置

```bash
# 环境变量
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-hangzhou.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"
```

### AWS S3配置

```bash
# 环境变量
export AWS_ACCESS_KEY_ID="your-access-key-id"
export AWS_SECRET_ACCESS_KEY="your-secret-access-key"
export S3_BUCKET_NAME="your-bucket-name"
export AWS_DEFAULT_REGION="us-west-2"  # 可选
```

### Azure Blob Storage配置

```bash
# 方式1: 连接字符串
export AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=..."
export AZURE_CONTAINER_NAME="your-container-name"

# 方式2: 账户名+密钥
export AZURE_STORAGE_ACCOUNT_NAME="your-account-name"
export AZURE_STORAGE_ACCOUNT_KEY="your-account-key"
export AZURE_CONTAINER_NAME="your-container-name"
```

## 🏃 运行方式

### 独立运行
```bash
cd file-storage
pip install -r requirements.txt
python server.py
```

### 作为子模块运行
通过主服务器的 `/file-storage/mcp` 端点访问。

## 📦 依赖项

### 核心依赖
- `mcp[cli]>=1.9.0`: MCP协议支持
- `python-dotenv>=1.0.0`: 环境变量管理
- `pydantic>=2.5.0`: 数据验证

### 云存储依赖
- `oss2>=2.18.0`: 阿里云OSS支持
- `boto3>=1.34.0`: AWS S3支持
- `azure-storage-blob>=12.19.0`: Azure Blob Storage支持

### 工具依赖
- `python-magic>=0.4.27`: 文件类型检测

## 🔒 安全注意事项

1. **凭证安全**: 不要在代码中硬编码访问密钥，使用环境变量
2. **权限控制**: 确保云存储账户具有适当的权限
3. **网络安全**: 在生产环境中使用HTTPS端点
4. **访问控制**: 生成的URL具有时效性，注意过期时间设置

## 🎯 最佳实践

1. **文件命名**: 工具会自动添加时间戳前缀，避免文件重名
2. **大文件处理**: 对于大文件，建议使用本地文件上传而非Base64编码
3. **错误处理**: 工具提供详细的错误信息，便于调试
4. **配置管理**: 优先使用环境变量，参数传递作为备用方案
5. **存储选择**: 根据地理位置和成本选择合适的存储提供商

## 📊 支持的文件格式

工具支持所有文件格式，包括但不限于：
- 图片: JPG, PNG, GIF, BMP, TIFF, WebP
- 文档: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- 视频: MP4, AVI, MOV, WMV, FLV
- 音频: MP3, WAV, AAC, FLAC
- 压缩包: ZIP, RAR, 7Z, TAR, GZ
- 代码文件: TXT, JSON, XML, HTML, CSS, JS, PY, JAVA

## 🔄 版本历史

- **v1.0.0**: 初始版本，支持OSS、S3、Azure三大云存储平台
