#!/usr/bin/env python3
"""
文件存储 MCP 工具
支持将文件上传到阿里云OSS存储服务
"""

import os
import base64
import hashlib
import mimetypes
import uuid
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

from mcp.server.fastmcp import FastMCP

# 初始化 FastMCP server，配置为无状态HTTP模式以支持多协议
mcp = FastMCP("file-storage", stateless_http=True)

# 配置
UPLOAD_DIR = os.path.join(os.path.dirname(__file__), "uploads")
os.makedirs(UPLOAD_DIR, exist_ok=True)

# OSS可用性检查
OSS_AVAILABLE = False

def check_oss_library():
    """检查OSS库的可用性"""
    global OSS_AVAILABLE

    try:
        import oss2
        OSS_AVAILABLE = True
    except ImportError:
        OSS_AVAILABLE = False

# 初始化时检查库可用性
check_oss_library()


class OSSStorageManager:
    """阿里云OSS存储管理器"""

    def __init__(self):
        self.oss_provider = None
        if OSS_AVAILABLE:
            self.oss_provider = OSSProvider()

    def is_available(self) -> bool:
        """检查OSS是否可用"""
        return OSS_AVAILABLE and self.oss_provider is not None

    def upload_file(self, file_path: str, object_name: str = None, **kwargs) -> Dict[str, Any]:
        """上传文件到OSS"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.upload_file(file_path, object_name, **kwargs)

    def upload_content(self, content: bytes, object_name: str, content_type: str = None, **kwargs) -> Dict[str, Any]:
        """上传内容到OSS"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.upload_content(content, object_name, content_type, **kwargs)

    def list_files(self, prefix: str = "", **kwargs) -> List[Dict[str, Any]]:
        """列出OSS文件"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.list_files(prefix, **kwargs)

    def delete_file(self, object_name: str, **kwargs) -> bool:
        """删除OSS文件"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.delete_file(object_name, **kwargs)

    def get_file_url(self, object_name: str, expires_in: int = 3600, **kwargs) -> str:
        """获取OSS文件访问URL"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.get_file_url(object_name, expires_in, **kwargs)


class OSSProvider:
    """阿里云OSS存储提供商"""

    def __init__(self):
        import oss2
        self.oss2 = oss2
        self._bucket = None

    def _get_bucket(self, **kwargs):
        """获取OSS bucket实例"""
        if self._bucket is None:
            access_key_id = kwargs.get('access_key_id') or os.getenv('OSS_ACCESS_KEY_ID')
            access_key_secret = kwargs.get('access_key_secret') or os.getenv('OSS_ACCESS_KEY_SECRET')
            endpoint = kwargs.get('endpoint') or os.getenv('OSS_ENDPOINT')
            bucket_name = kwargs.get('bucket_name') or os.getenv('OSS_BUCKET_NAME')

            if not all([access_key_id, access_key_secret, endpoint, bucket_name]):
                raise ValueError("OSS配置不完整，请提供access_key_id, access_key_secret, endpoint, bucket_name")

            auth = self.oss2.Auth(access_key_id, access_key_secret)
            self._bucket = self.oss2.Bucket(auth, endpoint, bucket_name)

        return self._bucket

    def upload_file(self, file_path: str, object_name: str = None, **kwargs) -> Dict[str, Any]:
        """上传文件到OSS"""
        bucket = self._get_bucket(**kwargs)

        if object_name is None:
            object_name = os.path.basename(file_path)

        # 添加时间戳前缀避免重名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        object_name = f"{timestamp}_{object_name}"

        try:
            result = bucket.put_object_from_file(object_name, file_path)

            return {
                "success": True,
                "provider": "oss",
                "object_name": object_name,
                "etag": result.etag,
                "url": f"https://{bucket.bucket_name}.{bucket.endpoint.replace('https://', '').replace('http://', '')}/{object_name}",
                "size": os.path.getsize(file_path)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def upload_content(self, content: bytes, object_name: str, content_type: str = None, **kwargs) -> Dict[str, Any]:
        """上传内容到OSS"""
        bucket = self._get_bucket(**kwargs)

        # 添加时间戳前缀避免重名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        object_name = f"{timestamp}_{object_name}"

        try:
            headers = {}
            if content_type:
                headers['Content-Type'] = content_type

            result = bucket.put_object(object_name, content, headers=headers)

            return {
                "success": True,
                "provider": "oss",
                "object_name": object_name,
                "etag": result.etag,
                "url": f"https://{bucket.bucket_name}.{bucket.endpoint.replace('https://', '').replace('http://', '')}/{object_name}",
                "size": len(content)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def list_files(self, prefix: str = "", **kwargs) -> List[Dict[str, Any]]:
        """列出OSS文件"""
        bucket = self._get_bucket(**kwargs)

        try:
            files = []
            for obj in self.oss2.ObjectIterator(bucket, prefix=prefix):
                files.append({
                    "name": obj.key,
                    "size": obj.size,
                    "last_modified": obj.last_modified,
                    "etag": obj.etag
                })
            return files
        except Exception as e:
            return []

    def delete_file(self, object_name: str, **kwargs) -> bool:
        """删除OSS文件"""
        bucket = self._get_bucket(**kwargs)

        try:
            bucket.delete_object(object_name)
            return True
        except Exception:
            return False

    def get_file_url(self, object_name: str, expires_in: int = 3600, **kwargs) -> str:
        """获取OSS文件访问URL"""
        bucket = self._get_bucket(**kwargs)

        try:
            url = bucket.sign_url('GET', object_name, expires_in)
            return url
        except Exception:
            return ""


# 创建存储管理器实例
storage_manager = OSSStorageManager()


def get_file_info(file_path: str) -> Dict[str, Any]:
    """获取文件信息"""
    if not os.path.exists(file_path):
        return {"error": "文件不存在"}

    stat = os.stat(file_path)
    mime_type, _ = mimetypes.guess_type(file_path)

    # 计算文件哈希
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)

    return {
        "name": os.path.basename(file_path),
        "size": stat.st_size,
        "mime_type": mime_type or "application/octet-stream",
        "md5": hash_md5.hexdigest(),
        "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
        "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
    }


@mcp.tool()
async def upload_file_to_oss(
    file_content_base64: str,
    filename: str,
    **oss_config
) -> str:
    """将文件上传到阿里云OSS

    Args:
        file_content_base64: Base64编码的文件内容
        filename: 文件名
        **oss_config: OSS配置参数

    Returns:
        上传结果信息
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2

📋 配置要求:
- OSS_ACCESS_KEY_ID
- OSS_ACCESS_KEY_SECRET
- OSS_ENDPOINT
- OSS_BUCKET_NAME
"""

        # 解码文件内容
        try:
            file_content = base64.b64decode(file_content_base64)
        except Exception as e:
            return f"❌ Base64解码失败: {str(e)}"

        # 检测文件类型
        mime_type, _ = mimetypes.guess_type(filename)
        if not mime_type:
            mime_type = "application/octet-stream"

        # 上传文件
        result = storage_manager.upload_content(
            content=file_content,
            object_name=filename,
            content_type=mime_type,
            **oss_config
        )

        if result.get("success"):
            return f"""✅ 文件上传成功！

📄 文件信息:
- 文件名: {filename}
- 大小: {result.get('size', 0):,} 字节
- 存储提供商: 阿里云OSS
- 对象名: {result.get('object_name', filename)}

🔗 访问链接:
{result.get('url', '未生成')}

📥 文件已成功上传到阿里云OSS。
"""
        else:
            return f"❌ 上传失败: {result.get('error', '未知错误')}"

    except Exception as e:
        return f"❌ 上传过程中发生错误: {str(e)}"


@mcp.tool()
async def upload_local_file_to_oss(
    file_path: str,
    object_name: Optional[str] = None,
    **oss_config
) -> str:
    """将本地文件上传到阿里云OSS

    Args:
        file_path: 本地文件路径
        object_name: OSS中的对象名（可选）
        **oss_config: OSS配置参数

    Returns:
        上传结果信息
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return f"❌ 文件不存在: {file_path}"

        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 获取文件信息
        file_info = get_file_info(file_path)
        if "error" in file_info:
            return f"❌ {file_info['error']}"

        # 上传文件
        result = storage_manager.upload_file(
            file_path=file_path,
            object_name=object_name,
            **oss_config
        )

        if result.get("success"):
            return f"""✅ 文件上传成功！

📄 文件信息:
- 原始文件: {file_info['name']}
- 大小: {file_info['size']:,} 字节
- 类型: {file_info['mime_type']}
- MD5: {file_info['md5']}
- 存储提供商: 阿里云OSS
- 对象名: {result.get('object_name', object_name or file_info['name'])}

🔗 访问链接:
{result.get('url', '未生成')}

📥 文件已成功上传到阿里云OSS。
"""
        else:
            return f"❌ 上传失败: {result.get('error', '未知错误')}"

    except Exception as e:
        return f"❌ 上传过程中发生错误: {str(e)}"


@mcp.tool()
async def list_oss_files(
    prefix: str = "",
    **oss_config
) -> str:
    """列出阿里云OSS中的文件

    Args:
        prefix: 文件名前缀过滤
        **oss_config: OSS配置参数

    Returns:
        文件列表信息
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 获取文件列表
        files = storage_manager.list_files(
            prefix=prefix,
            **oss_config
        )

        if not files:
            return "📁 阿里云OSS存储中没有找到文件"

        # 格式化文件列表
        result = "📁 阿里云OSS存储文件列表:\n\n"
        total_size = 0

        for file_info in files:
            size = file_info.get('size', 0)
            total_size += size
            last_modified = file_info.get('last_modified', 'Unknown')

            if isinstance(last_modified, datetime):
                last_modified = last_modified.strftime('%Y-%m-%d %H:%M:%S')

            result += f"📄 {file_info['name']}\n"
            result += f"   大小: {size:,} 字节\n"
            result += f"   修改时间: {last_modified}\n"
            if 'etag' in file_info:
                result += f"   ETag: {file_info['etag']}\n"
            result += "\n"

        result += f"📊 总计: {len(files)} 个文件，{total_size:,} 字节"
        return result

    except Exception as e:
        return f"❌ 获取文件列表失败: {str(e)}"


@mcp.tool()
async def delete_oss_file(
    object_name: str,
    **oss_config
) -> str:
    """删除阿里云OSS中的文件

    Args:
        object_name: 要删除的对象名
        **oss_config: OSS配置参数

    Returns:
        删除结果信息
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 删除文件
        success = storage_manager.delete_file(
            object_name=object_name,
            **oss_config
        )

        if success:
            return f"""✅ 文件删除成功！

📄 已删除文件:
- 对象名: {object_name}
- 存储提供商: 阿里云OSS

🗑️ 文件已从OSS中永久删除。
"""
        else:
            return f"❌ 删除失败: 文件可能不存在或没有权限"

    except Exception as e:
        return f"❌ 删除过程中发生错误: {str(e)}"


@mcp.tool()
async def get_oss_file_url(
    object_name: str,
    expires_in: int = 3600,
    **oss_config
) -> str:
    """获取阿里云OSS文件的访问URL

    Args:
        object_name: 对象名
        expires_in: URL过期时间（秒）
        **oss_config: OSS配置参数

    Returns:
        文件访问URL
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 获取文件URL
        url = storage_manager.get_file_url(
            object_name=object_name,
            expires_in=expires_in,
            **oss_config
        )

        if url:
            expires_time = datetime.now() + timedelta(seconds=expires_in)
            return f"""✅ 文件URL生成成功！

📄 文件信息:
- 对象名: {object_name}
- 存储提供商: 阿里云OSS

🔗 访问链接:
{url}

⏰ 链接有效期: {expires_time.strftime('%Y-%m-%d %H:%M:%S')}
（{expires_in} 秒后过期）

💡 请在有效期内使用此链接访问文件。
"""
        else:
            return f"❌ 无法生成访问URL: 文件可能不存在或配置有误"

    except Exception as e:
        return f"❌ 获取URL过程中发生错误: {str(e)}"


@mcp.tool()
async def get_storage_info() -> str:
    """获取文件存储工具的信息和使用说明

    Returns:
        工具信息和使用说明
    """
    oss_status = '✅ 可用' if OSS_AVAILABLE else '❌ 不可用 (需要: pip install oss2)'

    return f"""📚 阿里云OSS文件存储工具

🔧 存储服务:
- 阿里云OSS: {oss_status}

✨ 主要功能:
- 文件上传 (Base64内容或本地文件)
- 文件列表查看
- 文件删除
- 生成访问URL
- 自动文件类型检测

📁 本地上传目录: {UPLOAD_DIR}

🔑 配置要求:
- OSS_ACCESS_KEY_ID: 阿里云访问密钥ID
- OSS_ACCESS_KEY_SECRET: 阿里云访问密钥Secret
- OSS_ENDPOINT: OSS服务端点
- OSS_BUCKET_NAME: OSS存储桶名称

💡 使用提示:
1. 设置环境变量或在调用时传递配置参数
2. 文件上传时会自动添加时间戳前缀避免重名
3. 支持多种文件格式的MIME类型检测
4. 生成的URL有时效性，默认1小时过期

🛠️ 安装依赖:
pip install oss2

📋 环境变量示例:
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-hangzhou.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"
"""


@mcp.resource("file-storage://oss-info")
def get_oss_info() -> str:
    """获取阿里云OSS配置信息"""
    status = "✅ 可用" if OSS_AVAILABLE else "❌ 不可用"

    return f"""阿里云OSS存储信息:

状态: {status}

必需的环境变量:
- OSS_ACCESS_KEY_ID: 访问密钥ID
- OSS_ACCESS_KEY_SECRET: 访问密钥Secret
- OSS_ENDPOINT: OSS服务端点
- OSS_BUCKET_NAME: 存储桶名称

环境变量示例:
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-hangzhou.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"

常用端点:
- 华东1（杭州）: https://oss-cn-hangzhou.aliyuncs.com
- 华东2（上海）: https://oss-cn-shanghai.aliyuncs.com
- 华北1（青岛）: https://oss-cn-qingdao.aliyuncs.com
- 华北2（北京）: https://oss-cn-beijing.aliyuncs.com
- 华南1（深圳）: https://oss-cn-shenzhen.aliyuncs.com
"""


@mcp.resource("file-storage://config")
def get_config_template() -> str:
    """获取配置模板"""
    return """阿里云OSS配置模板:

# 方式1: 环境变量配置（推荐）
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-hangzhou.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"

# 方式2: 函数参数配置
await upload_file_to_oss(
    file_content_base64="...",
    filename="test.jpg",
    access_key_id="your-access-key-id",
    access_key_secret="your-access-key-secret",
    endpoint="https://oss-cn-hangzhou.aliyuncs.com",
    bucket_name="your-bucket-name"
)

注意事项:
1. 优先使用环境变量配置，更安全
2. 确保OSS bucket已创建并有适当权限
3. 选择距离用户最近的区域端点
4. 妥善保管访问密钥，不要泄露
"""


def main():
    """主函数，启动MCP服务器，使用Streamable HTTP协议"""
    mcp.run(transport='streamable-http')


if __name__ == "__main__":
    main()
