#!/usr/bin/env python3
"""
文件存储 MCP 工具
支持将文件上传到阿里云OSS存储服务
"""

import os
import base64
import hashlib
import mimetypes
import uuid
import json
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta

from mcp.server.fastmcp import FastMCP

# 初始化 FastMCP server，配置为无状态HTTP模式以支持多协议
mcp = FastMCP("file-storage", stateless_http=True)

# 配置
UPLOAD_DIR = os.path.join(os.path.dirname(__file__), "uploads")
os.makedirs(UPLOAD_DIR, exist_ok=True)

# OSS可用性检查
OSS_AVAILABLE = False

def check_oss_library():
    """检查OSS库的可用性"""
    global OSS_AVAILABLE

    try:
        import oss2
        OSS_AVAILABLE = True
    except ImportError:
        OSS_AVAILABLE = False

# 初始化时检查库可用性
check_oss_library()


class OSSConfig:
    """阿里云OSS配置管理类"""

    # 默认SSO配置
    DEFAULT_CONFIG = {
        'access_key_id': 'LTAI5tJbjspS4HdX3mGbXqco',
        'access_key_secret': '******************************',
        'endpoint': 'https://oss-cn-shenzhen.aliyuncs.com',
        'bucket_name': 'coli-ai'
    }

    def __init__(self):
        self.config = {}
        self._load_config()

    def _load_config(self):
        """加载配置，优先级：环境变量 > 配置文件 > 默认配置"""
        # 1. 从默认配置开始
        self.config = self.DEFAULT_CONFIG.copy()

        # 2. 尝试从配置文件加载
        config_file = os.path.join(os.path.dirname(__file__), 'oss_config.json')
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self.config.update(file_config)
            except Exception as e:
                print(f"警告: 无法加载配置文件 {config_file}: {e}")

        # 3. 环境变量覆盖
        env_mapping = {
            'OSS_ACCESS_KEY_ID': 'access_key_id',
            'OSS_ACCESS_KEY_SECRET': 'access_key_secret',
            'OSS_ENDPOINT': 'endpoint',
            'OSS_BUCKET_NAME': 'bucket_name'
        }

        for env_key, config_key in env_mapping.items():
            env_value = os.getenv(env_key)
            if env_value:
                self.config[config_key] = env_value

    def get_config(self, **kwargs) -> Dict[str, str]:
        """获取最终配置，参数传入的配置优先级最高"""
        final_config = self.config.copy()

        # 参数映射
        param_mapping = {
            'access_key_id': 'access_key_id',
            'access_key_secret': 'access_key_secret',
            'endpoint': 'endpoint',
            'bucket_name': 'bucket_name'
        }

        for param_key, config_key in param_mapping.items():
            if param_key in kwargs and kwargs[param_key]:
                final_config[config_key] = kwargs[param_key]

        return final_config

    def validate_config(self, config: Dict[str, str]) -> tuple[bool, str]:
        """验证配置是否完整"""
        required_keys = ['access_key_id', 'access_key_secret', 'endpoint', 'bucket_name']

        for key in required_keys:
            if not config.get(key):
                return False, f"缺少必需的配置项: {key}"

        # 验证endpoint格式
        endpoint = config['endpoint']
        if not endpoint.startswith(('http://', 'https://')):
            config['endpoint'] = f"https://{endpoint}"

        return True, "配置验证通过"

    def get_config_info(self) -> str:
        """获取当前配置信息（隐藏敏感信息）"""
        safe_config = {}
        for key, value in self.config.items():
            if 'secret' in key.lower() or 'key' in key.lower():
                safe_config[key] = f"{value[:4]}****{value[-4:]}" if len(value) > 8 else "****"
            else:
                safe_config[key] = value

        return json.dumps(safe_config, indent=2, ensure_ascii=False)


class OSSStorageManager:
    """阿里云OSS存储管理器"""

    def __init__(self):
        self.oss_provider = None
        self.config_manager = OSSConfig()
        if OSS_AVAILABLE:
            self.oss_provider = OSSProvider(self.config_manager)

    def is_available(self) -> bool:
        """检查OSS是否可用"""
        return OSS_AVAILABLE and self.oss_provider is not None

    def upload_file(self, file_path: str, object_name: str = None, **kwargs) -> Dict[str, Any]:
        """上传文件到OSS"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.upload_file(file_path, object_name, **kwargs)

    def upload_content(self, content: bytes, object_name: str, content_type: str = None, **kwargs) -> Dict[str, Any]:
        """上传内容到OSS"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.upload_content(content, object_name, content_type, **kwargs)

    def list_files(self, prefix: str = "", **kwargs) -> List[Dict[str, Any]]:
        """列出OSS文件"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.list_files(prefix, **kwargs)

    def delete_file(self, object_name: str, **kwargs) -> bool:
        """删除OSS文件"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.delete_file(object_name, **kwargs)

    def get_file_url(self, object_name: str, expires_in: int = 3600, **kwargs) -> str:
        """获取OSS文件访问URL"""
        if not self.is_available():
            raise ValueError("OSS不可用，请安装oss2库")

        return self.oss_provider.get_file_url(object_name, expires_in, **kwargs)


class OSSProvider:
    """阿里云OSS存储提供商"""

    def __init__(self, config_manager: OSSConfig):
        import oss2
        self.oss2 = oss2
        self.config_manager = config_manager
        self._bucket = None
        self._current_config = None

    def _get_bucket(self, **kwargs):
        """获取OSS bucket实例"""
        # 获取最终配置
        config = self.config_manager.get_config(**kwargs)

        # 验证配置
        is_valid, message = self.config_manager.validate_config(config)
        if not is_valid:
            raise ValueError(f"OSS配置错误: {message}")

        # 如果配置发生变化，重新创建bucket实例
        if self._bucket is None or self._current_config != config:
            try:
                auth = self.oss2.Auth(config['access_key_id'], config['access_key_secret'])
                self._bucket = self.oss2.Bucket(auth, config['endpoint'], config['bucket_name'])
                self._current_config = config.copy()
            except Exception as e:
                raise ValueError(f"创建OSS连接失败: {str(e)}")

        return self._bucket

    def upload_file(self, file_path: str, object_name: str = None, **kwargs) -> Dict[str, Any]:
        """上传文件到OSS"""
        bucket = self._get_bucket(**kwargs)

        if object_name is None:
            object_name = os.path.basename(file_path)

        # 添加时间戳前缀避免重名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        object_name = f"{timestamp}_{object_name}"

        try:
            result = bucket.put_object_from_file(object_name, file_path)

            return {
                "success": True,
                "provider": "oss",
                "object_name": object_name,
                "etag": result.etag,
                "url": f"https://{bucket.bucket_name}.{bucket.endpoint.replace('https://', '').replace('http://', '')}/{object_name}",
                "size": os.path.getsize(file_path)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def upload_content(self, content: bytes, object_name: str, content_type: str = None, **kwargs) -> Dict[str, Any]:
        """上传内容到OSS"""
        bucket = self._get_bucket(**kwargs)

        # 添加时间戳前缀避免重名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        object_name = f"{timestamp}_{object_name}"

        try:
            headers = {}
            if content_type:
                headers['Content-Type'] = content_type

            result = bucket.put_object(object_name, content, headers=headers)

            return {
                "success": True,
                "provider": "oss",
                "object_name": object_name,
                "etag": result.etag,
                "url": f"https://{bucket.bucket_name}.{bucket.endpoint.replace('https://', '').replace('http://', '')}/{object_name}",
                "size": len(content)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def list_files(self, prefix: str = "", **kwargs) -> List[Dict[str, Any]]:
        """列出OSS文件"""
        bucket = self._get_bucket(**kwargs)

        try:
            files = []
            for obj in self.oss2.ObjectIterator(bucket, prefix=prefix):
                files.append({
                    "name": obj.key,
                    "size": obj.size,
                    "last_modified": obj.last_modified,
                    "etag": obj.etag
                })
            return files
        except Exception as e:
            return []

    def delete_file(self, object_name: str, **kwargs) -> bool:
        """删除OSS文件"""
        bucket = self._get_bucket(**kwargs)

        try:
            bucket.delete_object(object_name)
            return True
        except Exception:
            return False

    def get_file_url(self, object_name: str, expires_in: int = 3600, **kwargs) -> str:
        """获取OSS文件访问URL"""
        bucket = self._get_bucket(**kwargs)

        try:
            url = bucket.sign_url('GET', object_name, expires_in)
            return url
        except Exception:
            return ""


# 创建存储管理器实例
storage_manager = OSSStorageManager()


def get_file_info(file_path: str) -> Dict[str, Any]:
    """获取文件信息"""
    if not os.path.exists(file_path):
        return {"error": "文件不存在"}

    stat = os.stat(file_path)
    mime_type, _ = mimetypes.guess_type(file_path)

    # 计算文件哈希
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)

    return {
        "name": os.path.basename(file_path),
        "size": stat.st_size,
        "mime_type": mime_type or "application/octet-stream",
        "md5": hash_md5.hexdigest(),
        "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
        "modified": datetime.fromtimestamp(stat.st_mtime).isoformat()
    }


@mcp.tool()
async def upload_file_to_oss(
    file_content_base64: str,
    filename: str,
    **oss_config
) -> str:
    """将文件上传到阿里云OSS

    Args:
        file_content_base64: Base64编码的文件内容
        filename: 文件名
        **oss_config: OSS配置参数

    Returns:
        上传结果信息
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2

📋 配置要求:
- OSS_ACCESS_KEY_ID
- OSS_ACCESS_KEY_SECRET
- OSS_ENDPOINT
- OSS_BUCKET_NAME
"""

        # 解码文件内容
        try:
            file_content = base64.b64decode(file_content_base64)
        except Exception as e:
            return f"❌ Base64解码失败: {str(e)}"

        # 检测文件类型
        mime_type, _ = mimetypes.guess_type(filename)
        if not mime_type:
            mime_type = "application/octet-stream"

        # 上传文件
        result = storage_manager.upload_content(
            content=file_content,
            object_name=filename,
            content_type=mime_type,
            **oss_config
        )

        if result.get("success"):
            return f"""✅ 文件上传成功！

📄 文件信息:
- 文件名: {filename}
- 大小: {result.get('size', 0):,} 字节
- 存储提供商: 阿里云OSS
- 对象名: {result.get('object_name', filename)}

🔗 访问链接:
{result.get('url', '未生成')}

📥 文件已成功上传到阿里云OSS。
"""
        else:
            return f"❌ 上传失败: {result.get('error', '未知错误')}"

    except Exception as e:
        return f"❌ 上传过程中发生错误: {str(e)}"


@mcp.tool()
async def upload_local_file_to_oss(
    file_path: str,
    object_name: Optional[str] = None,
    **oss_config
) -> str:
    """将本地文件上传到阿里云OSS

    Args:
        file_path: 本地文件路径
        object_name: OSS中的对象名（可选）
        **oss_config: OSS配置参数

    Returns:
        上传结果信息
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(file_path):
            return f"❌ 文件不存在: {file_path}"

        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 获取文件信息
        file_info = get_file_info(file_path)
        if "error" in file_info:
            return f"❌ {file_info['error']}"

        # 上传文件
        result = storage_manager.upload_file(
            file_path=file_path,
            object_name=object_name,
            **oss_config
        )

        if result.get("success"):
            return f"""✅ 文件上传成功！

📄 文件信息:
- 原始文件: {file_info['name']}
- 大小: {file_info['size']:,} 字节
- 类型: {file_info['mime_type']}
- MD5: {file_info['md5']}
- 存储提供商: 阿里云OSS
- 对象名: {result.get('object_name', object_name or file_info['name'])}

🔗 访问链接:
{result.get('url', '未生成')}

📥 文件已成功上传到阿里云OSS。
"""
        else:
            return f"❌ 上传失败: {result.get('error', '未知错误')}"

    except Exception as e:
        return f"❌ 上传过程中发生错误: {str(e)}"


@mcp.tool()
async def list_oss_files(
    prefix: str = "",
    **oss_config
) -> str:
    """列出阿里云OSS中的文件

    Args:
        prefix: 文件名前缀过滤
        **oss_config: OSS配置参数

    Returns:
        文件列表信息
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 获取文件列表
        files = storage_manager.list_files(
            prefix=prefix,
            **oss_config
        )

        if not files:
            return "📁 阿里云OSS存储中没有找到文件"

        # 格式化文件列表
        result = "📁 阿里云OSS存储文件列表:\n\n"
        total_size = 0

        for file_info in files:
            size = file_info.get('size', 0)
            total_size += size
            last_modified = file_info.get('last_modified', 'Unknown')

            if isinstance(last_modified, datetime):
                last_modified = last_modified.strftime('%Y-%m-%d %H:%M:%S')

            result += f"📄 {file_info['name']}\n"
            result += f"   大小: {size:,} 字节\n"
            result += f"   修改时间: {last_modified}\n"
            if 'etag' in file_info:
                result += f"   ETag: {file_info['etag']}\n"
            result += "\n"

        result += f"📊 总计: {len(files)} 个文件，{total_size:,} 字节"
        return result

    except Exception as e:
        return f"❌ 获取文件列表失败: {str(e)}"


@mcp.tool()
async def delete_oss_file(
    object_name: str,
    **oss_config
) -> str:
    """删除阿里云OSS中的文件

    Args:
        object_name: 要删除的对象名
        **oss_config: OSS配置参数

    Returns:
        删除结果信息
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 删除文件
        success = storage_manager.delete_file(
            object_name=object_name,
            **oss_config
        )

        if success:
            return f"""✅ 文件删除成功！

📄 已删除文件:
- 对象名: {object_name}
- 存储提供商: 阿里云OSS

🗑️ 文件已从OSS中永久删除。
"""
        else:
            return f"❌ 删除失败: 文件可能不存在或没有权限"

    except Exception as e:
        return f"❌ 删除过程中发生错误: {str(e)}"


@mcp.tool()
async def get_oss_file_url(
    object_name: str,
    expires_in: int = 3600,
    **oss_config
) -> str:
    """获取阿里云OSS文件的访问URL

    Args:
        object_name: 对象名
        expires_in: URL过期时间（秒）
        **oss_config: OSS配置参数

    Returns:
        文件访问URL
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 获取文件URL
        url = storage_manager.get_file_url(
            object_name=object_name,
            expires_in=expires_in,
            **oss_config
        )

        if url:
            expires_time = datetime.now() + timedelta(seconds=expires_in)
            return f"""✅ 文件URL生成成功！

📄 文件信息:
- 对象名: {object_name}
- 存储提供商: 阿里云OSS

🔗 访问链接:
{url}

⏰ 链接有效期: {expires_time.strftime('%Y-%m-%d %H:%M:%S')}
（{expires_in} 秒后过期）

💡 请在有效期内使用此链接访问文件。
"""
        else:
            return f"❌ 无法生成访问URL: 文件可能不存在或配置有误"

    except Exception as e:
        return f"❌ 获取URL过程中发生错误: {str(e)}"


@mcp.tool()
async def test_oss_connection(**oss_config) -> str:
    """测试阿里云OSS连接配置

    Args:
        **oss_config: OSS配置参数（可选，不提供则使用默认配置）

    Returns:
        连接测试结果
    """
    try:
        # 检查OSS是否可用
        if not storage_manager.is_available():
            return """❌ 阿里云OSS不可用

🔧 请安装OSS依赖:
pip install oss2
"""

        # 获取配置信息
        config = storage_manager.config_manager.get_config(**oss_config)
        is_valid, message = storage_manager.config_manager.validate_config(config)

        if not is_valid:
            return f"❌ 配置验证失败: {message}"

        # 尝试连接OSS
        try:
            # 通过列出文件来测试连接
            files = storage_manager.list_files(prefix="", **oss_config)

            # 获取bucket信息
            bucket = storage_manager.oss_provider._get_bucket(**oss_config)
            bucket_info = bucket.get_bucket_info()

            return f"""✅ OSS连接测试成功！

📋 配置信息:
{storage_manager.config_manager.get_config_info()}

🪣 存储桶信息:
- 存储桶名称: {bucket_info.name}
- 创建时间: {bucket_info.creation_date}
- 存储类型: {bucket_info.storage_class}
- 位置: {bucket_info.location}

📊 连接状态:
- 状态: ✅ 正常
- 文件数量: {len(files) if isinstance(files, list) else 0}

💡 配置来源优先级:
1. 函数参数 (最高优先级)
2. 环境变量
3. 配置文件 (oss_config.json)
4. 默认SSO配置 (最低优先级)
"""

        except Exception as e:
            return f"""❌ OSS连接测试失败

📋 配置信息:
{storage_manager.config_manager.get_config_info()}

❌ 错误详情:
{str(e)}

🔧 可能的解决方案:
1. 检查访问密钥是否正确
2. 确认存储桶名称是否存在
3. 验证endpoint地址是否正确
4. 检查网络连接是否正常
5. 确认账户权限是否足够
"""

    except Exception as e:
        return f"❌ 测试过程中发生错误: {str(e)}"


@mcp.tool()
async def create_oss_config_file(
    access_key_id: Optional[str] = None,
    access_key_secret: Optional[str] = None,
    endpoint: Optional[str] = None,
    bucket_name: Optional[str] = None
) -> str:
    """创建OSS配置文件

    Args:
        access_key_id: 访问密钥ID (可选，不提供则使用默认值)
        access_key_secret: 访问密钥Secret (可选，不提供则使用默认值)
        endpoint: OSS端点 (可选，不提供则使用默认值)
        bucket_name: 存储桶名称 (可选，不提供则使用默认值)

    Returns:
        配置文件创建结果
    """
    try:
        # 使用提供的参数或默认配置
        config = {
            'access_key_id': access_key_id or OSSConfig.DEFAULT_CONFIG['access_key_id'],
            'access_key_secret': access_key_secret or OSSConfig.DEFAULT_CONFIG['access_key_secret'],
            'endpoint': endpoint or OSSConfig.DEFAULT_CONFIG['endpoint'],
            'bucket_name': bucket_name or OSSConfig.DEFAULT_CONFIG['bucket_name']
        }

        # 配置文件路径
        config_file = os.path.join(os.path.dirname(__file__), 'oss_config.json')

        # 写入配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        # 隐藏敏感信息用于显示
        safe_config = {}
        for key, value in config.items():
            if 'secret' in key.lower() or 'key' in key.lower():
                safe_config[key] = f"{value[:4]}****{value[-4:]}" if len(value) > 8 else "****"
            else:
                safe_config[key] = value

        return f"""✅ OSS配置文件创建成功！

📁 配置文件位置:
{config_file}

📋 配置内容:
{json.dumps(safe_config, indent=2, ensure_ascii=False)}

💡 使用说明:
1. 配置文件优先级低于环境变量和函数参数
2. 可以手动编辑配置文件修改设置
3. 重启服务后配置生效
4. 建议妥善保管配置文件，避免泄露访问密钥

🔧 测试配置:
使用 test_oss_connection 工具测试配置是否正确
"""

    except Exception as e:
        return f"❌ 创建配置文件失败: {str(e)}"


@mcp.tool()
async def get_storage_info() -> str:
    """获取文件存储工具的信息和使用说明

    Returns:
        工具信息和使用说明
    """
    oss_status = '✅ 可用' if OSS_AVAILABLE else '❌ 不可用 (需要: pip install oss2)'

    return f"""📚 阿里云OSS文件存储工具

🔧 存储服务:
- 阿里云OSS: {oss_status}

✨ 主要功能:
- 文件上传 (Base64内容或本地文件)
- 文件列表查看
- 文件删除
- 生成访问URL
- 自动文件类型检测
- OSS连接测试

📁 本地上传目录: {UPLOAD_DIR}

🔑 当前配置信息:
{storage_manager.config_manager.get_config_info() if storage_manager.config_manager else '配置管理器未初始化'}

🔧 配置优先级 (从高到低):
1. 函数参数配置 (调用时传入)
2. 环境变量配置
3. 配置文件 (oss_config.json)
4. 默认SSO配置

💡 使用提示:
1. 已内置默认SSO配置，开箱即用
2. 可通过环境变量或参数覆盖默认配置
3. 文件上传时会自动添加时间戳前缀避免重名
4. 支持多种文件格式的MIME类型检测
5. 生成的URL有时效性，默认1小时过期
6. 使用 test_oss_connection 工具测试连接

🛠️ 安装依赖:
pip install oss2

📋 环境变量示例:
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-shenzhen.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"

🔧 配置文件示例 (oss_config.json):
{{
  "access_key_id": "your-access-key-id",
  "access_key_secret": "your-access-key-secret",
  "endpoint": "https://oss-cn-shenzhen.aliyuncs.com",
  "bucket_name": "your-bucket-name"
}}
"""


@mcp.resource("file-storage://oss-info")
def get_oss_info() -> str:
    """获取阿里云OSS配置信息"""
    status = "✅ 可用" if OSS_AVAILABLE else "❌ 不可用"

    return f"""阿里云OSS存储信息:

状态: {status}

🔧 配置优先级 (从高到低):
1. 函数参数配置 (调用时传入)
2. 环境变量配置
3. 配置文件 (oss_config.json)
4. 默认SSO配置 (已内置)

📋 默认SSO配置:
- 访问密钥ID: LTAI****Xqco
- 端点: https://oss-cn-shenzhen.aliyuncs.com
- 存储桶: coli-ai

🔑 环境变量配置:
- OSS_ACCESS_KEY_ID: 访问密钥ID
- OSS_ACCESS_KEY_SECRET: 访问密钥Secret
- OSS_ENDPOINT: OSS服务端点
- OSS_BUCKET_NAME: 存储桶名称

环境变量示例:
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-shenzhen.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"

📁 配置文件示例 (oss_config.json):
{{
  "access_key_id": "your-access-key-id",
  "access_key_secret": "your-access-key-secret",
  "endpoint": "https://oss-cn-shenzhen.aliyuncs.com",
  "bucket_name": "your-bucket-name"
}}

🌍 常用端点:
- 华东1（杭州）: https://oss-cn-hangzhou.aliyuncs.com
- 华东2（上海）: https://oss-cn-shanghai.aliyuncs.com
- 华北1（青岛）: https://oss-cn-qingdao.aliyuncs.com
- 华北2（北京）: https://oss-cn-beijing.aliyuncs.com
- 华南1（深圳）: https://oss-cn-shenzhen.aliyuncs.com

🔧 工具功能:
- test_oss_connection: 测试OSS连接
- create_oss_config_file: 创建配置文件
"""


@mcp.resource("file-storage://config")
def get_config_template() -> str:
    """获取配置模板"""
    return """阿里云OSS配置模板:

🔧 配置优先级 (从高到低):
1. 函数参数配置 (调用时传入)
2. 环境变量配置
3. 配置文件 (oss_config.json)
4. 默认SSO配置 (已内置，开箱即用)

# 方式1: 环境变量配置（推荐）
export OSS_ACCESS_KEY_ID="your-access-key-id"
export OSS_ACCESS_KEY_SECRET="your-access-key-secret"
export OSS_ENDPOINT="https://oss-cn-shenzhen.aliyuncs.com"
export OSS_BUCKET_NAME="your-bucket-name"

# 方式2: 配置文件 (oss_config.json)
{
  "access_key_id": "your-access-key-id",
  "access_key_secret": "your-access-key-secret",
  "endpoint": "https://oss-cn-shenzhen.aliyuncs.com",
  "bucket_name": "your-bucket-name"
}

# 方式3: 函数参数配置
await upload_file_to_oss(
    file_content_base64="...",
    filename="test.jpg",
    access_key_id="your-access-key-id",
    access_key_secret="your-access-key-secret",
    endpoint="https://oss-cn-shenzhen.aliyuncs.com",
    bucket_name="your-bucket-name"
)

# 方式4: 使用默认SSO配置 (无需配置)
await upload_file_to_oss(
    file_content_base64="...",
    filename="test.jpg"
)

💡 使用建议:
1. 开发测试: 使用默认SSO配置，开箱即用
2. 生产环境: 使用环境变量配置，更安全
3. 个人使用: 可使用配置文件，方便管理
4. 临时使用: 通过函数参数传递配置

🔧 工具命令:
- create_oss_config_file: 创建配置文件
- test_oss_connection: 测试连接配置

注意事项:
1. 默认配置已内置，可直接使用
2. 确保OSS bucket已创建并有适当权限
3. 选择距离用户最近的区域端点
4. 妥善保管访问密钥，不要泄露
5. 配置文件不要提交到版本控制系统
"""


def main():
    """主函数，启动MCP服务器，使用Streamable HTTP协议"""
    mcp.run(transport='streamable-http')


if __name__ == "__main__":
    main()
