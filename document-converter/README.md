# 文档格式转换器

一个强大的MCP工具，支持将Markdown文档转换为Word和PDF格式，提供专业的文档格式化和样式支持。

## 🚀 功能特性

- **Markdown到Word转换**: 支持将Markdown文档转换为Microsoft Word格式(.docx)
- **Markdown到PDF转换**: 支持将Markdown文档转换为PDF格式，带有专业样式
- **批量转换**: 支持同时转换为Word和PDF格式
- **丰富的Markdown语法支持**: 标题、段落、表格、代码块、列表、引用等
- **专业PDF样式**: 自定义CSS样式，包含彩色标题、代码高亮、表格样式等
- **文件管理**: 自动文件命名、输出目录管理、文件列表查看

## 🛠️ 工具列表

### convert_markdown_to_word
将Markdown内容转换为Word文档
- **参数**:
  - `markdown_content` (必需): Markdown格式的文档内容
  - `filename` (可选): 输出文件名（不包含扩展名）
- **返回**: 转换结果信息和文件路径

### convert_markdown_to_pdf
将Markdown内容转换为PDF文档
- **参数**:
  - `markdown_content` (必需): Markdown格式的文档内容
  - `filename` (可选): 输出文件名（不包含扩展名）
- **返回**: 转换结果信息和文件路径

### convert_markdown_to_both
将Markdown内容同时转换为Word和PDF文档
- **参数**:
  - `markdown_content` (必需): Markdown格式的文档内容
  - `filename` (可选): 输出文件名（不包含扩展名）
- **返回**: 两个文件的转换结果信息

### list_output_files
列出输出目录中的所有转换文件
- **返回**: 文件列表，包含文件名、大小、修改时间等信息

### get_conversion_info
获取文档转换工具的信息和使用说明
- **返回**: 工具功能介绍和使用指南

## 📚 资源列表

### document-converter://output/{filename}
获取指定输出文件的详细信息
- **参数**: `filename` - 文件名
- **返回**: 文件大小、修改时间、路径等信息

### document-converter://formats
获取支持的文档格式信息
- **返回**: 支持的输入输出格式和Markdown语法说明

## 💡 使用示例

### 基本转换示例

```python
# 转换为Word文档
result = await convert_markdown_to_word(
    markdown_content="# 标题\n\n这是一个**粗体**文本示例。",
    filename="my_document"
)

# 转换为PDF文档
result = await convert_markdown_to_pdf(
    markdown_content="# 标题\n\n这是一个*斜体*文本示例。",
    filename="my_document"
)

# 同时转换为两种格式
result = await convert_markdown_to_both(
    markdown_content="# 标题\n\n这是一个包含`代码`的示例。",
    filename="my_document"
)
```

### 复杂Markdown示例

```markdown
# 项目报告

## 概述
这是一个**重要**的项目报告。

## 数据表格
| 项目 | 进度 | 状态 |
|------|------|------|
| 任务A | 80% | 进行中 |
| 任务B | 100% | 完成 |

## 代码示例
```python
def hello_world():
    print("Hello, World!")
```

> 这是一个重要的引用块。

### 待办事项
- [x] 完成设计
- [ ] 实现功能
- [ ] 测试验证
```

## 🏃 运行方式

### 独立运行
```bash
cd document-converter
pip install -r requirements.txt
python server.py
```

### 作为子模块运行
通过主服务器的 `/document-converter/mcp` 端点访问。

## 📦 依赖项

### 核心依赖
- `mcp[cli]>=1.9.0`: MCP协议支持
- `markdown>=3.5.0`: Markdown解析
- `python-docx>=1.1.0`: Word文档生成
- `weasyprint>=60.0`: PDF生成
- `beautifulsoup4>=4.12.0`: HTML解析
- `Pillow>=10.0.0`: 图像处理支持

### 可选依赖
- `pypandoc>=1.12`: 增强的文档转换
- `reportlab>=4.0.0`: 高级PDF功能

## 🎨 PDF样式特性

- **专业字体**: Arial字体，优化的行间距
- **彩色标题**: 蓝色主题的标题样式
- **代码高亮**: 灰色背景的代码块
- **表格样式**: 带边框和表头高亮的表格
- **引用样式**: 左侧蓝色边框的引用块
- **响应式布局**: 适合打印和屏幕阅读

## 📁 输出目录

转换后的文件保存在 `document-converter/output/` 目录中，支持：
- 自动创建输出目录
- 唯一文件名生成
- 文件大小和时间戳记录
- 批量文件管理
