#!/usr/bin/env python3
"""
文档格式转换 MCP 工具
支持将Markdown文档转换为Word和PDF格式
"""

import os
import io
import base64
import tempfile
import uuid
from typing import Optional, Dict, Any
from pathlib import Path

# 导入文档处理库
import markdown
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from bs4 import BeautifulSoup

# PDF生成库可用性检查（延迟导入）
WEASYPRINT_AVAILABLE = False
REPORTLAB_AVAILABLE = False

def check_pdf_libraries():
    """检查PDF生成库的可用性"""
    global WEASYPRINT_AVAILABLE, REPORTLAB_AVAILABLE

    # 检查WeasyPrint（更安全的检查方式）
    try:
        import importlib.util
        spec = importlib.util.find_spec("weasyprint")
        if spec is not None:
            # 尝试实际导入以检查依赖
            import weasyprint
            WEASYPRINT_AVAILABLE = True
        else:
            WEASYPRINT_AVAILABLE = False
    except Exception:
        WEASYPRINT_AVAILABLE = False

    # 检查ReportLab
    try:
        import importlib.util
        spec = importlib.util.find_spec("reportlab")
        if spec is not None:
            from reportlab.lib.pagesizes import A4
            REPORTLAB_AVAILABLE = True
        else:
            REPORTLAB_AVAILABLE = False
    except Exception:
        REPORTLAB_AVAILABLE = False

# 初始化时检查库可用性
check_pdf_libraries()

from mcp.server.fastmcp import FastMCP

# 初始化 FastMCP server，配置为无状态HTTP模式以支持多协议
mcp = FastMCP("document-converter", stateless_http=True)

# 配置
OUTPUT_DIR = os.path.join(os.path.dirname(__file__), "output")
os.makedirs(OUTPUT_DIR, exist_ok=True)


class DocumentConverter:
    """文档转换器类"""

    def __init__(self):
        self.markdown_extensions = [
            'markdown.extensions.tables',
            'markdown.extensions.fenced_code',
            'markdown.extensions.codehilite',
            'markdown.extensions.toc',
            'markdown.extensions.attr_list'
        ]

    def markdown_to_html(self, markdown_content: str) -> str:
        """将Markdown转换为HTML"""
        md = markdown.Markdown(extensions=self.markdown_extensions)
        html = md.convert(markdown_content)
        return html

    def markdown_to_word(self, markdown_content: str, output_path: str) -> str:
        """将Markdown转换为Word文档"""
        # 创建Word文档
        doc = Document()

        # 解析Markdown内容
        html = self.markdown_to_html(markdown_content)
        soup = BeautifulSoup(html, 'html.parser')

        # 处理HTML元素并添加到Word文档
        self._process_html_elements(doc, soup)

        # 保存文档
        doc.save(output_path)
        return output_path

    def _process_html_elements(self, doc: Document, soup: BeautifulSoup):
        """处理HTML元素并添加到Word文档"""
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'table', 'pre', 'blockquote']):
            if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                # 处理标题
                level = int(element.name[1])
                heading = doc.add_heading(element.get_text().strip(), level=level)

            elif element.name == 'p':
                # 处理段落
                paragraph = doc.add_paragraph()
                self._process_inline_elements(paragraph, element)

            elif element.name in ['ul', 'ol']:
                # 处理列表
                for li in element.find_all('li', recursive=False):
                    paragraph = doc.add_paragraph(li.get_text().strip(), style='List Bullet' if element.name == 'ul' else 'List Number')

            elif element.name == 'table':
                # 处理表格
                self._process_table(doc, element)

            elif element.name == 'pre':
                # 处理代码块
                code_text = element.get_text()
                paragraph = doc.add_paragraph(code_text)
                paragraph.style = 'No Spacing'
                for run in paragraph.runs:
                    run.font.name = 'Courier New'

            elif element.name == 'blockquote':
                # 处理引用
                quote_text = element.get_text().strip()
                paragraph = doc.add_paragraph(quote_text)
                paragraph.style = 'Quote'

    def _process_inline_elements(self, paragraph, element):
        """处理内联元素"""
        for content in element.contents:
            if hasattr(content, 'name'):
                if content.name == 'strong' or content.name == 'b':
                    run = paragraph.add_run(content.get_text())
                    run.bold = True
                elif content.name == 'em' or content.name == 'i':
                    run = paragraph.add_run(content.get_text())
                    run.italic = True
                elif content.name == 'code':
                    run = paragraph.add_run(content.get_text())
                    run.font.name = 'Courier New'
                else:
                    paragraph.add_run(content.get_text())
            else:
                paragraph.add_run(str(content))

    def _process_table(self, doc: Document, table_element):
        """处理表格"""
        rows = table_element.find_all('tr')
        if not rows:
            return

        # 计算列数
        max_cols = max(len(row.find_all(['td', 'th'])) for row in rows)

        # 创建表格
        table = doc.add_table(rows=len(rows), cols=max_cols)
        table.style = 'Table Grid'

        # 填充表格内容
        for i, row in enumerate(rows):
            cells = row.find_all(['td', 'th'])
            for j, cell in enumerate(cells):
                if j < max_cols:
                    table.cell(i, j).text = cell.get_text().strip()
                    # 如果是表头，设置为粗体
                    if cell.name == 'th':
                        for paragraph in table.cell(i, j).paragraphs:
                            for run in paragraph.runs:
                                run.bold = True

    def markdown_to_pdf(self, markdown_content: str, output_path: str) -> str:
        """将Markdown转换为PDF文档"""
        if WEASYPRINT_AVAILABLE:
            return self._markdown_to_pdf_weasyprint(markdown_content, output_path)
        elif REPORTLAB_AVAILABLE:
            return self._markdown_to_pdf_reportlab(markdown_content, output_path)
        else:
            raise ImportError("没有可用的PDF生成库。请安装 weasyprint 或 reportlab。")

    def _markdown_to_pdf_weasyprint(self, markdown_content: str, output_path: str) -> str:
        """使用WeasyPrint将Markdown转换为PDF"""
        import weasyprint

        # 将Markdown转换为HTML
        html = self.markdown_to_html(markdown_content)

        # 添加CSS样式
        styled_html = self._add_pdf_styles(html)

        # 使用WeasyPrint转换为PDF
        weasyprint.HTML(string=styled_html).write_pdf(output_path)
        return output_path

    def _markdown_to_pdf_reportlab(self, markdown_content: str, output_path: str) -> str:
        """使用ReportLab将Markdown转换为PDF"""
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors

        # 解析Markdown内容
        html = self.markdown_to_html(markdown_content)
        soup = BeautifulSoup(html, 'html.parser')

        # 创建PDF文档
        doc = SimpleDocTemplate(output_path, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []

        # 自定义样式
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=12,
            textColor=colors.HexColor('#2c3e50')
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=8,
            textColor=colors.HexColor('#34495e')
        )

        # 处理HTML元素
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'table', 'pre']):
            if element.name == 'h1':
                story.append(Paragraph(element.get_text().strip(), title_style))
            elif element.name in ['h2', 'h3', 'h4', 'h5', 'h6']:
                story.append(Paragraph(element.get_text().strip(), heading_style))
            elif element.name == 'p':
                story.append(Paragraph(element.get_text().strip(), styles['Normal']))
            elif element.name in ['ul', 'ol']:
                for li in element.find_all('li', recursive=False):
                    story.append(Paragraph(f"• {li.get_text().strip()}", styles['Normal']))
            elif element.name == 'pre':
                story.append(Paragraph(element.get_text().strip(), styles['Code']))
            elif element.name == 'table':
                # 简单的表格处理
                rows = element.find_all('tr')
                if rows:
                    table_data = []
                    for row in rows:
                        cells = row.find_all(['td', 'th'])
                        table_data.append([cell.get_text().strip() for cell in cells])

                    if table_data:
                        table = Table(table_data)
                        table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                            ('FONTSIZE', (0, 0), (-1, 0), 14),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(table)

            story.append(Spacer(1, 12))

        # 构建PDF
        doc.build(story)
        return output_path

    def _add_pdf_styles(self, html: str) -> str:
        """为PDF添加CSS样式"""
        css_styles = """
        <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            margin-top: 24px;
            margin-bottom: 16px;
        }
        h1 { font-size: 2em; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        h2 { font-size: 1.5em; border-bottom: 1px solid #bdc3c7; padding-bottom: 8px; }
        h3 { font-size: 1.3em; }
        p { margin-bottom: 16px; }
        code {
            background-color: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        pre {
            background-color: #f8f9fa;
            padding: 16px;
            border-radius: 6px;
            border-left: 4px solid #3498db;
            overflow-x: auto;
        }
        blockquote {
            border-left: 4px solid #3498db;
            margin: 16px 0;
            padding-left: 16px;
            color: #7f8c8d;
            font-style: italic;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid #bdc3c7;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #ecf0f1;
            font-weight: bold;
        }
        ul, ol {
            margin: 16px 0;
            padding-left: 32px;
        }
        li {
            margin-bottom: 8px;
        }
        </style>
        """
        return f"<!DOCTYPE html><html><head><meta charset='utf-8'>{css_styles}</head><body>{html}</body></html>"


# 创建转换器实例
converter = DocumentConverter()


@mcp.tool()
async def convert_markdown_to_word(markdown_content: str, filename: Optional[str] = None) -> str:
    """将Markdown内容转换为Word文档

    Args:
        markdown_content: Markdown格式的文档内容
        filename: 输出文件名（可选，不包含扩展名）

    Returns:
        转换结果信息，包含文件路径和下载链接
    """
    try:
        # 生成文件名
        if not filename:
            filename = f"document_{uuid.uuid4().hex[:8]}"

        output_path = os.path.join(OUTPUT_DIR, f"{filename}.docx")

        # 执行转换
        result_path = converter.markdown_to_word(markdown_content, output_path)

        # 计算文件大小
        file_size = os.path.getsize(result_path)

        return f"""✅ Markdown转Word转换成功！

📄 文件信息:
- 文件名: {os.path.basename(result_path)}
- 文件大小: {file_size:,} 字节
- 保存路径: {result_path}

📥 文件已保存到输出目录，可以通过文件系统访问。
"""

    except Exception as e:
        return f"❌ 转换失败: {str(e)}"


@mcp.tool()
async def convert_markdown_to_pdf(markdown_content: str, filename: Optional[str] = None) -> str:
    """将Markdown内容转换为PDF文档

    Args:
        markdown_content: Markdown格式的文档内容
        filename: 输出文件名（可选，不包含扩展名）

    Returns:
        转换结果信息，包含文件路径和下载链接
    """
    try:
        # 检查PDF生成库可用性
        if not WEASYPRINT_AVAILABLE and not REPORTLAB_AVAILABLE:
            return """❌ PDF转换不可用

🔧 需要安装以下依赖之一:
1. WeasyPrint (推荐): pip install weasyprint
   - 在macOS上可能需要: brew install pango
2. ReportLab (备用): pip install reportlab

💡 提示: 您仍然可以使用Word转换功能。
"""

        # 生成文件名
        if not filename:
            filename = f"document_{uuid.uuid4().hex[:8]}"

        output_path = os.path.join(OUTPUT_DIR, f"{filename}.pdf")

        # 执行转换
        result_path = converter.markdown_to_pdf(markdown_content, output_path)

        # 计算文件大小
        file_size = os.path.getsize(result_path)

        # 确定使用的PDF引擎
        pdf_engine = "WeasyPrint" if WEASYPRINT_AVAILABLE else "ReportLab"

        return f"""✅ Markdown转PDF转换成功！

📄 文件信息:
- 文件名: {os.path.basename(result_path)}
- 文件大小: {file_size:,} 字节
- 保存路径: {result_path}
- PDF引擎: {pdf_engine}

📥 文件已保存到输出目录，可以通过文件系统访问。
"""

    except Exception as e:
        return f"❌ 转换失败: {str(e)}"


@mcp.tool()
async def convert_markdown_to_both(markdown_content: str, filename: Optional[str] = None) -> str:
    """将Markdown内容同时转换为Word和PDF文档

    Args:
        markdown_content: Markdown格式的文档内容
        filename: 输出文件名（可选，不包含扩展名）

    Returns:
        转换结果信息，包含两个文件的路径
    """
    try:
        # 生成文件名
        if not filename:
            filename = f"document_{uuid.uuid4().hex[:8]}"

        word_path = os.path.join(OUTPUT_DIR, f"{filename}.docx")
        pdf_path = os.path.join(OUTPUT_DIR, f"{filename}.pdf")

        # 执行转换
        word_result = converter.markdown_to_word(markdown_content, word_path)
        pdf_result = converter.markdown_to_pdf(markdown_content, pdf_path)

        # 计算文件大小
        word_size = os.path.getsize(word_result)
        pdf_size = os.path.getsize(pdf_result)

        return f"""✅ Markdown转换完成！

📄 Word文档:
- 文件名: {os.path.basename(word_result)}
- 文件大小: {word_size:,} 字节
- 保存路径: {word_result}

📄 PDF文档:
- 文件名: {os.path.basename(pdf_result)}
- 文件大小: {pdf_size:,} 字节
- 保存路径: {pdf_result}

📥 文件已保存到输出目录，可以通过文件系统访问。
"""

    except Exception as e:
        return f"❌ 转换失败: {str(e)}"


@mcp.tool()
async def list_output_files() -> str:
    """列出输出目录中的所有转换文件

    Returns:
        输出目录中文件的列表
    """
    try:
        if not os.path.exists(OUTPUT_DIR):
            return "📁 输出目录不存在或为空"

        files = []
        for filename in os.listdir(OUTPUT_DIR):
            if filename.endswith(('.docx', '.pdf')):
                file_path = os.path.join(OUTPUT_DIR, filename)
                file_size = os.path.getsize(file_path)
                file_mtime = os.path.getmtime(file_path)

                files.append({
                    'name': filename,
                    'size': file_size,
                    'modified': file_mtime,
                    'path': file_path
                })

        if not files:
            return "📁 输出目录中没有找到转换文件"

        # 按修改时间排序
        files.sort(key=lambda x: x['modified'], reverse=True)

        result = "📁 输出目录文件列表:\n\n"
        for file_info in files:
            import datetime
            mod_time = datetime.datetime.fromtimestamp(file_info['modified']).strftime('%Y-%m-%d %H:%M:%S')
            result += f"📄 {file_info['name']}\n"
            result += f"   大小: {file_info['size']:,} 字节\n"
            result += f"   修改时间: {mod_time}\n"
            result += f"   路径: {file_info['path']}\n\n"

        return result.strip()

    except Exception as e:
        return f"❌ 获取文件列表失败: {str(e)}"


@mcp.tool()
async def get_conversion_info() -> str:
    """获取文档转换工具的信息和使用说明

    Returns:
        工具信息和使用说明
    """
    # 检查PDF库可用性
    pdf_status = ""
    if WEASYPRINT_AVAILABLE:
        pdf_status = "✅ WeasyPrint (高质量PDF)"
    elif REPORTLAB_AVAILABLE:
        pdf_status = "✅ ReportLab (基础PDF)"
    else:
        pdf_status = "❌ 不可用 (需要安装 weasyprint 或 reportlab)"

    return f"""📚 文档格式转换工具

🔧 支持的转换格式:
- Markdown → Word (.docx) ✅
- Markdown → PDF (.pdf) {pdf_status}
- Markdown → Word + PDF (同时转换)

✨ 支持的Markdown特性:
- 标题 (H1-H6)
- 段落和换行
- 粗体和斜体文本
- 代码块和内联代码
- 表格
- 列表 (有序和无序)
- 引用块
- 链接

🎨 PDF样式特性:
- 专业的字体和排版
- 彩色标题和边框
- 代码高亮背景
- 表格样式
- 引用块样式

📁 输出目录: {OUTPUT_DIR}

🔧 PDF引擎状态:
- WeasyPrint: {'✅ 可用' if WEASYPRINT_AVAILABLE else '❌ 不可用'}
- ReportLab: {'✅ 可用' if REPORTLAB_AVAILABLE else '❌ 不可用'}

💡 使用提示:
1. 提供Markdown内容和可选的文件名
2. 文件会自动保存到输出目录
3. 使用 list_output_files 查看已转换的文件
4. 支持复杂的Markdown语法和表格

🛠️ 安装PDF支持:
- macOS: brew install pango && pip install weasyprint
- 或者: pip install reportlab (备用方案)
"""


@mcp.resource("document-converter://output/{filename}")
def get_output_file(filename: str) -> str:
    """获取输出文件信息"""
    file_path = os.path.join(OUTPUT_DIR, filename)
    if os.path.exists(file_path):
        file_size = os.path.getsize(file_path)
        import datetime
        mod_time = datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')

        return f"""文件信息: {filename}
大小: {file_size:,} 字节
修改时间: {mod_time}
路径: {file_path}
"""
    else:
        return f"文件不存在: {filename}"


@mcp.resource("document-converter://formats")
def get_supported_formats() -> str:
    """获取支持的文档格式信息"""
    return """支持的文档格式转换:

输入格式:
- Markdown (.md)

输出格式:
- Microsoft Word (.docx)
- PDF (.pdf)

支持的Markdown语法:
- 标题 (# ## ### ...)
- 段落
- 粗体 (**text**)
- 斜体 (*text*)
- 代码块 (```code```)
- 内联代码 (`code`)
- 表格
- 列表
- 引用块 (> text)
- 链接 [text](url)
"""


def main():
    """主函数，启动MCP服务器，使用Streamable HTTP协议"""
    mcp.run(transport='streamable-http')


if __name__ == "__main__":
    main()
